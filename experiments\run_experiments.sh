#!/bin/bash
###
 # @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 # @Date: 2025-06-28 21:23:27
 # @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 # @LastEditTime: 2025-06-29 10:30:53
 # @FilePath: \Multi-resolution-Energy-Forecasting-master\experiments\run_experiments.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

fcst_steps=288
observe_steps=$((2 * fcst_steps))
window_width=$((fcst_steps + observe_steps))

python example.py -d nrel --add_external_feature --observe_steps $observe_steps --window_width $window_width --latent_dim 16 --avg_terms_list 12 3 1 --gpu 0 --encoder rnn --pass_raw --shared_encoder --run_times 20
python example.py -d mfred --add_external_feature --observe_steps $observe_steps --window_width $window_width --latent_dim 16 --avg_terms_list 12 3 1 --gpu 0 --encoder dnn --pass_raw --shared_encoder --run_times 20

python example_benchmarks.py -d nrel --add_external_feature --observe_steps $observe_steps --window_width $window_width --gpu 0 --avg_terms_list 12 3 1 --run_times 20 --persistence naive 
python example_benchmarks.py -d mfred --add_external_feature --observe_steps $observe_steps --window_width $window_width --gpu 0 --avg_terms_list 12 3 1 --run_times 20 --persistence loop 

python dayahead_opt.py
python integrated_opt.py

