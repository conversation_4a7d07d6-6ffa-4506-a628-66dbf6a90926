{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/multiresolution_forecasting/venv/lib/python3.8/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import pickle\n", "import torch\n", "import numpy as np\n", "from copy import deepcopy\n", "from sklearn.metrics import mean_squared_error"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Benchmarks\n", "Please replace the variable ```pth``` into your new result."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. No adjusts"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">Neural Laplace</th>\n", "      <th colspan=\"2\" halign=\"left\">LSTM</th>\n", "      <th colspan=\"2\" halign=\"left\">MLP</th>\n", "      <th colspan=\"2\" halign=\"left\">Persistence</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.936882</td>\n", "      <td>0.380235</td>\n", "      <td>5.188830</td>\n", "      <td>0.09685</td>\n", "      <td>5.149303</td>\n", "      <td>0.091195</td>\n", "      <td>6.596242</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.971793</td>\n", "      <td>0.267169</td>\n", "      <td>5.307560</td>\n", "      <td>0.19586</td>\n", "      <td>4.927910</td>\n", "      <td>0.118691</td>\n", "      <td>6.603842</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>4.998867</td>\n", "      <td>0.349172</td>\n", "      <td>5.403205</td>\n", "      <td>0.15979</td>\n", "      <td>4.680242</td>\n", "      <td>0.086694</td>\n", "      <td>6.640212</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Neural Laplace                LSTM                MLP             \n", "             mean       std      mean      std      mean       std   \n", "1        4.936882  0.380235  5.188830  0.09685  5.149303  0.091195  \\\n", "3        4.971793  0.267169  5.307560  0.19586  4.927910  0.118691   \n", "12       4.998867  0.349172  5.403205  0.15979  4.680242  0.086694   \n", "\n", "   Persistence       \n", "          mean  std  \n", "1     6.596242  0.0  \n", "3     6.603842  0.0  \n", "12    6.640212  0.0  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# electricity load dataset (persistence: loop)\n", "# pth = \"results/mfred/example_benchmarks-20230831-041824\"\n", "\n", "# wind power dataset (persistence: naive)\n", "pth = \"results/nrel/example_benchmarks-20230817-231811\"\n", "# pth = \"results/mfred/example_benchmarks-20230814-210128\"\n", "\n", "# NL with one large Laplace decoder (failure)\n", "# pth = \"results/mfred/example_benchmarks-20231012-175829\"\n", "\n", "# model_names = ['<PERSON><PERSON><PERSON>', 'LSTM', 'MLP']\n", "model_names = ['<PERSON><PERSON><PERSON>', 'LSTM', 'MLP', 'Persistence']\n", "# model_names = ['<PERSON><PERSON><PERSON>']\n", "fcst_features = [0]\n", "all_seed_result = []\n", "all_seed_preds = []\n", "for seed in range(20):\n", "    with open(f\"{pth}-{seed}.pkl\", \"rb\") as f:\n", "        all_result = pickle.load(f)\n", "    test_result = {name: {} for name in model_names}\n", "    test_preds_trajs = {name: {} for name in model_names}\n", "    for avg_terms in all_result:\n", "        result_avg = all_result[avg_terms]\n", "        train_mean = result_avg['train_mean'][fcst_features]\n", "        train_std = result_avg['train_std'][fcst_features]\n", "        num_avg_terms = int(avg_terms.split(\"_\")[-1])\n", "        for name in model_names:\n", "            model_results = result_avg[name]\n", "\n", "            test_preds = model_results[\"test_preds\"] * train_std.cpu().numpy(\n", "            ) + train_mean.cpu().numpy()\n", "            test_trajs = model_results[\"test_trajs\"] * train_std.cpu().numpy(\n", "            ) + train_mean.cpu().numpy()\n", "            pred_timesteps = test_trajs.shape[1]\n", "            # if num_avg_terms == 1:\n", "            #     fig, ax = plt.subplots()\n", "            #     ax.plot(test_trajs[300,:,0], label=\"real\")\n", "            #     ax.plot(test_preds[300,:,0], label=\"fcst\")\n", "            #     ax.legend()\n", "            #     ax.set(xlabel=\"Forecasting horizon\", ylabel=\"Load(kW)\")\n", "            #     # ax.set_title(name)\n", "            #     fig.savefig(\"savings/supp_NL.pdf\")\n", "            test_result[name][num_avg_terms] = mean_squared_error(\n", "                test_trajs.squeeze(), test_preds.squeeze(), squared=False)\n", "            test_preds_trajs[name][num_avg_terms] = deepcopy(\n", "                (test_preds, test_trajs))\n", "    df_test_result = pd.DataFrame(test_result)\n", "    all_seed_preds.append(test_preds_trajs)\n", "    all_seed_result.append(df_test_result)\n", "all_seed_result = pd.concat(all_seed_result, axis=0)\n", "all_seed_result = all_seed_result.groupby(all_seed_result.index).agg(\n", "    [\"mean\", \"std\"])\n", "all_seed_result = all_seed_result.sort_index()\n", "avg_terms_list = all_seed_result.index.tolist()\n", "all_seed_result\n", "# with open(f'savings/bench_20_{pth.split(\"/\")[1]}.pickle', 'wb') as handle:\n", "#     pickle.dump(all_seed_preds, handle, protocol=pickle.HIGHEST_PROTOCOL)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["mean consistent error"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>5min vs 15min</th>\n", "      <th>5min vs 60min</th>\n", "      <th>15min vs 60min</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>6.425709</td>\n", "      <td>7.581275</td>\n", "      <td>7.389087</td>\n", "    </tr>\n", "    <tr>\n", "      <th>LSTM</th>\n", "      <td>2.006866</td>\n", "      <td>5.986436</td>\n", "      <td>5.944163</td>\n", "    </tr>\n", "    <tr>\n", "      <th>MLP</th>\n", "      <td>2.083820</td>\n", "      <td>3.931973</td>\n", "      <td>2.498962</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                5min vs 15min  5min vs 60min  15min vs 60min\n", "<PERSON><PERSON><PERSON>       6.425709       7.581275        7.389087\n", "LSTM                 2.006866       5.986436        5.944163\n", "MLP                  2.083820       3.931973        2.498962"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["max_avg_terms = max(avg_terms_list)\n", "mce_results = {name: [] for name in model_names}\n", "for seed in range(20):\n", "    preds_trajs_dict = all_seed_preds[seed]\n", "    for name in model_names:\n", "        mce = {\"5min vs 15min\":0, \"5min vs 60min\":0, \"15min vs 60min\":0}\n", "        for n, avg_terms in enumerate(avg_terms_list):\n", "\n", "            if avg_terms < max_avg_terms:\n", "                # print(\"current avg_terms\", avg_terms)\n", "                rest_avg_terms = avg_terms_list[n + 1:]\n", "                (test_preds, test_trajs) = preds_trajs_dict[name][avg_terms]\n", "                for r_avg_terms in rest_avg_terms:\n", "                    (real_avg_preds,\n", "                     real_avg_trajs) = preds_trajs_dict[name][r_avg_terms]\n", "                    # print(avg_terms, r_avg_terms)\n", "                    # calculate predictions\n", "                    avg_test_preds = np.split(test_preds,\n", "                                              test_preds.shape[1] //\n", "                                              (r_avg_terms // avg_terms),\n", "                                              axis=1)\n", "                    avg_test_preds = np.stack(\n", "                        [j.mean(axis=1) for j in avg_test_preds], axis=1)\n", "                    error = (avg_test_preds - real_avg_preds)**2\n", "                    mce[f\"{avg_terms*5}min vs {r_avg_terms*5}min\"] += error.mean()\n", "        # mce = pd.DataFrame(mce, index=0)\n", "        # print(mce)\n", "        mce_results[name].append(mce)\n", "\n", "all_model_mce = []\n", "for name in model_names:\n", "    \n", "    model_mce = pd.DataFrame(mce_results[name]).mean()\n", "    all_model_mce.append(model_mce)\n", "all_model_mce = pd.concat(all_model_mce, axis=1)\n", "all_model_mce = all_model_mce.T\n", "all_model_mce.index = model_names\n", "all_model_mce = all_model_mce.drop(\"Persistence\", axis=0)\n", "all_model_mce\n", "# mce_results.index = [0 for _ in range(20)]\n", "# mce_results = mce_results.groupby(mce_results.index).agg(\n", "    # [\"mean\", \"std\"])\n", "# mce_results.index= [\"MCE\"]\n", "# mce_results.mean()\n", "# mce_results.to_csv(f\"savings/bench_{pth.split('/')[1]}_mce.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. After adjusted"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1 Bottom up"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">Neural Laplace-BU</th>\n", "      <th colspan=\"2\" halign=\"left\">LSTM-BU</th>\n", "      <th colspan=\"2\" halign=\"left\">MLP-BU</th>\n", "      <th colspan=\"2\" halign=\"left\">Persistence-BU</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.994273</td>\n", "      <td>0.405666</td>\n", "      <td>5.317320</td>\n", "      <td>0.103603</td>\n", "      <td>5.214271</td>\n", "      <td>0.093587</td>\n", "      <td>6.884816</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.987119</td>\n", "      <td>0.406156</td>\n", "      <td>5.306487</td>\n", "      <td>0.104206</td>\n", "      <td>5.191459</td>\n", "      <td>0.092432</td>\n", "      <td>6.879671</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>4.927970</td>\n", "      <td>0.410021</td>\n", "      <td>5.250381</td>\n", "      <td>0.105409</td>\n", "      <td>5.129826</td>\n", "      <td>0.092773</td>\n", "      <td>6.837363</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Neural Laplace-BU             LSTM-BU              MLP-BU             \n", "                mean       std      mean       std      mean       std   \n", "1           4.994273  0.405666  5.317320  0.103603  5.214271  0.093587  \\\n", "3           4.987119  0.406156  5.306487  0.104206  5.191459  0.092432   \n", "12          4.927970  0.410021  5.250381  0.105409  5.129826  0.092773   \n", "\n", "   Persistence-BU       \n", "             mean  std  \n", "1        6.884816  0.0  \n", "3        6.879671  0.0  \n", "12       6.837363  0.0  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["all_bu_results, all_bu_preds = [], []\n", "for seed in range(20):\n", "    preds_trajs_dict = all_seed_preds[seed]\n", "    bu_results = {name + \"-BU\": {} for name in model_names}\n", "    bu_preds = {name + \"-BU\": {} for name in model_names}\n", "    for name in model_names:\n", "        for avg_terms in avg_terms_list:\n", "            (test_preds, test_trajs) = preds_trajs_dict[name][1]\n", "            # Test trajs are consistent\n", "            avg_test_trajs = np.split(test_trajs,\n", "                                      test_trajs.shape[1] // avg_terms,\n", "                                      axis=1)\n", "            avg_test_trajs = np.stack([j.mean(axis=1) for j in avg_test_trajs],\n", "                                      axis=1)\n", "\n", "            # calculate predictions\n", "            avg_test_preds = np.split(test_preds,\n", "                                      test_preds.shape[1] // avg_terms,\n", "                                      axis=1)\n", "            avg_test_preds = np.stack([j.mean(axis=1) for j in avg_test_preds],\n", "                                      axis=1)\n", "            bu_results[name + \"-BU\"][avg_terms] = mean_squared_error(\n", "                avg_test_trajs.flatten(),\n", "                avg_test_preds.flatten(),\n", "                squared=False)\n", "            bu_preds[name + \"-BU\"][avg_terms] = (avg_test_preds,avg_test_trajs\n", "                                                 )\n", "\n", "            # if avg_terms == 3:\n", "            # fig, ax = plt.subplots()\n", "            # ax.plot(avg_test_trajs[0,:,0])\n", "            # ax.plot(avg_test_preds[0,:,0])\n", "\n", "    bu_results = pd.DataFrame(bu_results)\n", "    all_bu_results.append(bu_results)\n", "    all_bu_preds.append(bu_preds)\n", "all_bu_results = pd.concat(all_bu_results, axis=0)\n", "all_bu_results = all_bu_results.groupby(all_bu_results.index).agg(\n", "    [\"mean\", \"std\"])\n", "all_bu_results = all_bu_results.sort_index()\n", "all_bu_results\n", "# with open(f'savings/bench_bu_20_{pth.split(\"/\")[1]}.pickle', 'wb') as handle:\n", "#     pickle.dump(all_bu_preds, handle, protocol=pickle.HIGHEST_PROTOCOL)\n", "# all_bu_results.to_csv(\"savings/benchmark_bu.csv\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Optimized"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, et al. \"Forecasting with temporal hierarchies.\" European Journal of Operational Research 262.1 (2017): 60-74."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">Neural Laplace-OPT</th>\n", "      <th colspan=\"2\" halign=\"left\">LSTM-OPT</th>\n", "      <th colspan=\"2\" halign=\"left\">MLP-OPT</th>\n", "      <th colspan=\"2\" halign=\"left\">Persistence-OPT</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.860383</td>\n", "      <td>0.331628</td>\n", "      <td>5.299112</td>\n", "      <td>0.091206</td>\n", "      <td>5.089091</td>\n", "      <td>0.076269</td>\n", "      <td>6.883998</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.853041</td>\n", "      <td>0.332068</td>\n", "      <td>5.288247</td>\n", "      <td>0.091614</td>\n", "      <td>5.065711</td>\n", "      <td>0.075272</td>\n", "      <td>6.878853</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>4.792563</td>\n", "      <td>0.335591</td>\n", "      <td>5.231973</td>\n", "      <td>0.092663</td>\n", "      <td>5.003829</td>\n", "      <td>0.075829</td>\n", "      <td>6.836540</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Neural Laplace-OPT            LSTM-OPT             MLP-OPT             \n", "                 mean       std      mean       std      mean       std   \n", "1            4.860383  0.331628  5.299112  0.091206  5.089091  0.076269  \\\n", "3            4.853041  0.332068  5.288247  0.091614  5.065711  0.075272   \n", "12           4.792563  0.335591  5.231973  0.092663  5.003829  0.075829   \n", "\n", "   Persistence-OPT       \n", "              mean  std  \n", "1         6.883998  0.0  \n", "3         6.878853  0.0  \n", "12        6.836540  0.0  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from scipy.linalg import block_diag\n", "\n", "S = [\n", "    block_diag(*[[1.0 / s for _ in range(s)]\n", "                 for _ in range(pred_timesteps // s)]) for s in avg_terms_list\n", "]\n", "S.reverse()\n", "S = np.concatenate(S)\n", "S = S @ np.linalg.inv(S.T @ S) @ S.T\n", "\n", "all_opt_results, all_opt_preds = [], []\n", "for seed in range(20):\n", "    preds_trajs_dict = all_seed_preds[seed]\n", "    opt_results = {name + \"-OPT\": {} for name in model_names}\n", "    opt_preds = {name + \"-OPT\": {} for name in model_names}\n", "    for name in model_names:\n", "        preds_vec, trajs_vec, pred_steps = [], [], []\n", "        for avg_terms in avg_terms_list[::-1]:\n", "            (test_preds, test_trajs) = preds_trajs_dict[name][avg_terms]\n", "            preds_vec.append(test_preds.copy())\n", "            trajs_vec.append(test_trajs.copy())\n", "        preds_vec = np.concatenate(preds_vec, axis=1)\n", "        # preds_vec = preds_vec.transpose(1, 0, 2)\n", "        adjusted_preds = np.tensordot(S, preds_vec, axes=[1, 1])\n", "        start = 0\n", "        for i, trajs in enumerate(trajs_vec):\n", "            steps = trajs.shape[1]\n", "            opt_results[name +\n", "                        \"-OPT\"][avg_terms_list[::-1][i]] = mean_squared_error(\n", "                            trajs.flatten(),\n", "                            adjusted_preds[start:start + steps,\n", "                                           ...].transpose(1, 0, 2).flatten(),\n", "                            squared=False)\n", "            opt_preds[name + \"-OPT\"][avg_terms_list[::-1][i]] = (\n", "                adjusted_preds[start:start + steps, ...].transpose(1, 0,\n", "                                                                   2), trajs)\n", "            # if i == 0:\n", "            #     fig, ax = plt.subplots()\n", "            #     ax.plot(trajs[0].flatten())\n", "            #     ax.plot(adjusted_preds[start:start + steps, 0, :].flatten())\n", "            #     print(adjusted_preds[start:start + steps, 0, :].flatten())\n", "            start += steps\n", "\n", "    opt_results = pd.DataFrame(opt_results)\n", "    all_opt_results.append(opt_results)\n", "    all_opt_preds.append(opt_preds)\n", "\n", "all_opt_results = pd.concat(all_opt_results, axis=0)\n", "all_opt_results = all_opt_results.groupby(all_opt_results.index).agg(\n", "    [\"mean\", \"std\"])\n", "all_opt_results = all_opt_results.sort_index()\n", "# all_opt_results.to_csv(\"savings/benchmark_opt.csv\")\n", "# with open(f'savings/bench_opt_20_{pth.split(\"/\")[1]}.pickle', 'wb') as handle:\n", "#     pickle.dump(all_opt_preds, handle, protocol=pickle.HIGHEST_PROTOCOL)\n", "all_opt_results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Hierarchical NL"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Please replace the variable ```pth``` into your new result."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">Hierarchical NL</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.641777</td>\n", "      <td>0.252007</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.616905</td>\n", "      <td>0.248384</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>4.538334</td>\n", "      <td>0.250710</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Hierarchical NL          \n", "              mean       std\n", "1         4.641777  0.252007\n", "3         4.616905  0.248384\n", "12        4.538334  0.250710"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import pickle\n", "import numpy as np\n", "from sklearn.metrics import mean_squared_error\n", "import pandas as pd\n", "\n", "\n", "# Load\n", "# pth = \"results/mfred/example-20230815-052223\"\n", "\n", "# Wind\n", "pth = \"results/nrel/example-20230819-070441\"\n", "\n", "\n", "all_seed_result = []\n", "all_seed_preds = []\n", "fcst_features = [0]\n", "for seed in range(20):\n", "    with open(f\"{pth}-{seed}.pkl\", \"rb\") as f:\n", "        result = pickle.load(f)\n", "        avg_terms_list = result[\"model_hyperparams\"][\"avg_terms_list\"]\n", "        # print(result[\"model_hyperparams\"][\"encoder\"])\n", "        train_mean = result[\"train_mean\"][fcst_features].detach().cpu().numpy()\n", "        train_std = result[\"train_std\"][fcst_features].detach().cpu().numpy()\n", "\n", "        model = result[\"Hierarchical NL\"]\n", "        # avg_terms_list = result[\"avg_terms_list\"]\n", "        # print(result[\"train_mean\"])\n", "        # print(result[\"train_std\"])\n", "        test_label = model[\"test_trajs\"]\n", "        test_preds = model[\"test_preds\"]\n", "        test_label = test_label\n", "        test_label = test_label * train_std + train_mean\n", "        pred_timesteps = test_label.shape[1]\n", "\n", "    avg_test_label, avg_test_preds, rmse = {}, {}, {}\n", "    for i, avg_terms in enumerate(avg_terms_list):\n", "        temp = np.split(test_label, test_label.shape[1] // avg_terms, axis=1)\n", "        temp = np.stack([j.mean(axis=1) for j in temp], axis=1)\n", "        avg_test_label[avg_terms] = temp\n", "        # avg_test_preds[avg_terms] = (test_preds[i].detach().cpu().numpy(\n", "        # ), temp)\n", "        avg_test_preds[avg_terms] = (\n", "            test_preds[i].detach().cpu().numpy() * train_std + train_mean,\n", "            temp)\n", "\n", "        # print(avg_test_preds[avg_terms][0].flatten())\n", "        rmse[avg_terms] = [\n", "            mean_squared_error(temp.squeeze(),\n", "                               avg_test_preds[avg_terms][0].squeeze(),\n", "                               squared=False)\n", "        ]\n", "        # fig, ax = plt.subplots()\n", "        # ax.plot(temp[3,:,0])\n", "        # ax.plot(avg_test_preds[avg_terms][0][3,:,0])\n", "    rmse = pd.DataFrame(rmse).transpose()\n", "    all_seed_result.append(rmse)\n", "    all_seed_preds.append(avg_test_preds)\n", "all_seed_result = pd.concat(all_seed_result, axis=0)\n", "all_seed_result = all_seed_result.groupby(all_seed_result.index).agg(\n", "    [\"mean\", \"std\"])\n", "avg_terms_list = all_seed_result.index.tolist()\n", "all_seed_result = all_seed_result.rename(columns={0: \"Hierarchical NL\"})\n", "all_seed_result\n", "\n", "# with open(f'savings/proposed_20_{pth.split(\"/\")[1]}.pickle', 'wb') as handle:\n", "#     pickle.dump(all_seed_preds, handle, protocol=pickle.HIGHEST_PROTOCOL)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["mean consistence error"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["seed  5min vs 15min  5min vs 60min  15min vs 60min\n", "0          0.026094       0.222991        0.135184\n", "1          0.006738       0.086766        0.063191\n", "2          0.072298       0.472124        0.297841\n", "3          0.008204       0.151570        0.117880\n", "4          0.050868       0.284752        0.160015\n", "5          0.037831       0.270309        0.151035\n", "6          0.041395       0.323505        0.176930\n", "7          0.006382       0.125603        0.090764\n", "8          0.010175       0.172496        0.126226\n", "9          0.065932       0.472889        0.260358\n", "10         0.022158       0.215032        0.128669\n", "11         0.075076       0.805726        0.516497\n", "12         0.027309       0.180347        0.103134\n", "13         0.067109       0.433050        0.230927\n", "14         0.060076       0.613812        0.363339\n", "15         0.038062       0.286358        0.149500\n", "16         0.017503       0.201247        0.126916\n", "17         0.008451       0.131688        0.089743\n", "18         0.014225       0.178021        0.122130\n", "19         0.062784       0.256006        0.132002\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>LSTM</th>\n", "      <th>MLP</th>\n", "      <th>HNL</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5min vs 15min</th>\n", "      <td>6.425709</td>\n", "      <td>2.006866</td>\n", "      <td>2.083820</td>\n", "      <td>0.035933</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5min vs 60min</th>\n", "      <td>7.581275</td>\n", "      <td>5.986436</td>\n", "      <td>3.931973</td>\n", "      <td>0.294215</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15min vs 60min</th>\n", "      <td>7.389087</td>\n", "      <td>5.944163</td>\n", "      <td>2.498962</td>\n", "      <td>0.177114</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Neural Laplace      LSTM       MLP       HNL\n", "5min vs 15min         6.425709  2.006866  2.083820  0.035933\n", "5min vs 60min         7.581275  5.986436  3.931973  0.294215\n", "15min vs 60min        7.389087  5.944163  2.498962  0.177114"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["max_avg_terms = max(avg_terms_list)\n", "mce_results = {}\n", "for seed in range(20):\n", "    preds_trajs_dict = all_seed_preds[seed]\n", "    mce = {\"5min vs 15min\":0, \"5min vs 60min\":0, \"15min vs 60min\":0}\n", "    for n, avg_terms in enumerate(avg_terms_list):\n", "\n", "        if avg_terms < max_avg_terms:\n", "            # print(\"current avg_terms\", avg_terms)\n", "            rest_avg_terms = avg_terms_list[n + 1:]\n", "            test_preds, _ = preds_trajs_dict[avg_terms]\n", "            for r_avg_terms in rest_avg_terms:\n", "                real_avg_preds, _ = preds_trajs_dict[r_avg_terms]\n", "\n", "                # print(avg_terms, r_avg_terms)\n", "                # calculate predictions\n", "                avg_test_preds = np.split(test_preds,\n", "                                          test_preds.shape[1] //\n", "                                          (r_avg_terms // avg_terms),\n", "                                          axis=1)\n", "                avg_test_preds = np.stack(\n", "                    [j.mean(axis=1) for j in avg_test_preds], axis=1)\n", "                error = (avg_test_preds - real_avg_preds)**2\n", "                mce[f\"{avg_terms*5}min vs {r_avg_terms*5}min\"] += error.mean()\n", "    mce_results[seed] = mce\n", "    # mce_results[seed] = [mce]\n", "mce_results = pd.DataFrame(mce_results)\n", "mce_results.index.name = \"seed\"\n", "mce_results = mce_results.transpose()\n", "print(mce_results)\n", "# mce_results.columns=[\"Hierarchical NL\"]\n", "# mce_results.index = [0 for _ in range(20)]\n", "mce_results = mce_results.mean()\n", "# mce_results.index= [\"MCE\"]\n", "all_model_mce = pd.concat([all_model_mce.T, mce_results], axis=1)\n", "all_model_mce.rename(columns={0:\"HNL\"}, inplace=True)\n", "all_model_mce\n", "# hnl = pd.concat([all_seed_result, mce_results])\n", "# benchmarks = pd.read_csv(\"savings/benchmark_raw.csv\", index_col=0, header=[0,1])\n", "# benchmarks.index = hnl.index\n", "# all_results = pd.concat([benchmarks, hnl], axis=1)\n", "# print(all_results)\n", "# mce_results.to_csv(f\"savings/proposed_{pth.split('/')[1]}_mce.csv\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["all_model_mce = all_model_mce.sort_values(by = \"15min vs 60min\", axis=1)\n", "all_model_mce = all_model_mce.rename(columns={\"Neural Laplace\":\"NL\"})\n", "# all_model_mce.to_csv(\"wind_mce.csv\")\n", "colors = plt.cm.BuPu(np.linspace(0.1, 0.5, len(all_model_mce)))\n", "n_rows = len(all_model_mce)\n", "\n", "index = np.arange(len(all_model_mce.columns))\n", "bar_width = 0.6\n", "\n", "# Initialize the vertical-offset for the stacked bar chart.\n", "y_offset = np.zeros(len(all_model_mce.columns))\n", "\n", "fig, ax=plt.subplots()\n", "# Plot bars and create text labels for the table\n", "cell_text = []\n", "for row in range(n_rows):\n", "    ax.bar(all_model_mce.columns, all_model_mce.iloc[row, :], bar_width, bottom=y_offset, color=colors[row], label=all_model_mce.index[row])\n", "    y_offset = y_offset + all_model_mce.iloc[row, :]\n", "\n", "ax.legend(fontsize=12)\n", "for i, tick in enumerate(ax.get_xticklabels()):\n", "    tick.set_fontsize(14)\n", "\n", "    if i == 0:\n", "        tick.set_fontweight('bold')\n", "        tick.set_fontsize(16)\n", "\n", "for i, tick in enumerate(ax.get_yticklabels()):\n", "    tick.set_fontsize(12)\n", "ax.ticklabel_format(style='sci', scilimits=(-1,2), axis='y')\n", "# fig.savefig(\"savings/mce_wind.pdf\",bbox_inches=\"tight\")\n"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}