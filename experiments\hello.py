'''
Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
Date: 2025-06-29 22:44:26
LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
LastEditTime: 2025-07-01 22:17:28
FilePath: \Multi-resolution-Energy-Forecasting-master\experiments\hello.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import sys
#sys.path.append("..")
sys.path.append("..")

import os
print("当前路径为:", os.getcwd())
import numpy as np
import torch
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

# 设置中文字体
plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]
plt.rcParams["axes.unicode_minus"] = False  # 解决负号显示问题

# 你提供的sine函数
def sine(double=False, trajectories_to_sample=100, t_nsamples=200, num_pi=4):
    t_nsamples_ref = 1000
    t_nsamples = int(t_nsamples_ref / 4 * num_pi)

    t_end = num_pi * np.pi
    t_begin = t_end / t_nsamples

    if double:
        ti = torch.linspace(t_begin, t_end, t_nsamples).double()
    else:
        ti = torch.linspace(t_begin, t_end, t_nsamples)

    def sampler(t, x0=0):
        return torch.sin(t + x0) + torch.sin(2 * (t + x0)) + 0.5 * torch.sin(12 * (t + x0))

    x0s = torch.linspace(0, 16 * torch.pi, trajectories_to_sample)
    trajs = []
    for x0 in x0s:
        trajs.append(sampler(ti, x0))
    y = torch.stack(trajs)
    trajectories = y.view(trajectories_to_sample, -1, 1)
    sample_rate = t_nsamples / ti.diff().sum() * 2 * np.pi
    print(f"采样率: {sample_rate:.2f} rad/单位时间")
    features = {
        "hist_feature": [0],
        "fcst_feature": [0],
        "avail_fcst_feature": None,
    }
    return trajectories, ti.unsqueeze(0), sample_rate, features

def plot_sine_waves(trajectories, time, num_plots=5, title="正弦波轨迹示例"):
    """
    绘制多个正弦波轨迹
    
    参数:
    trajectories: 轨迹数据，形状为 [轨迹数, 时间步, 1]
    time: 时间点，形状为 [1, 时间步]
    num_plots: 要显示的轨迹数量
    title: 图表标题
    """
    plt.figure(figsize=(12, 6))
    
    # 从time中获取一维数组
    t = time.squeeze().numpy()
    
    for i in range(min(num_plots, trajectories.shape[0])):
        # 从trajectories中获取一维数组
        y = trajectories[i].squeeze().numpy()
        plt.plot(t, y, label=f'轨迹 {i+1}')
    
    plt.title(title)
    plt.xlabel('时间')
    plt.ylabel('振幅')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.show()

def plot_waveform_distribution(trajectories, time, title="波形分布"):
    """
    绘制所有波形的分布，使用半透明线条叠加显示
    
    参数:
    trajectories: 轨迹数据，形状为 [轨迹数, 时间步, 1]
    time: 时间点，形状为 [1, 时间步]
    title: 图表标题
    """
    plt.figure(figsize=(12, 6))
    
    # 从time中获取一维数组
    t = time.squeeze().numpy()
    
    for i in range(trajectories.shape[0]):
        # 从trajectories中获取一维数组
        y = trajectories[i].squeeze().numpy()
        plt.plot(t, y, alpha=0.2, color='blue')
    
    plt.title(title)
    plt.xlabel('时间')
    plt.ylabel('振幅')
    plt.grid(True)
    plt.tight_layout()
    plt.show()

def create_animation(trajectories, time, title="正弦波动画"):
    """
    创建正弦波动画
    
    参数:
    trajectories: 轨迹数据，形状为 [轨迹数, 时间步, 1]
    time: 时间点，形状为 [1, 时间步]
    title: 图表标题
    """
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 从time中获取一维数组
    t = time.squeeze().numpy()
    
    # 初始化线条
    line, = ax.plot([], [], lw=2)
    
    # 设置坐标轴范围
    ax.set_xlim(t[0], t[-1])
    ax.set_ylim(-3, 3)
    ax.set_xlabel('时间')
    ax.set_ylabel('振幅')
    ax.set_title(title)
    ax.grid(True)
    
    # 初始化函数
    def init():
        line.set_data([], [])
        return line,
    
    # 更新函数
    def update(frame):
        # 从trajectories中获取一维数组
        y = trajectories[frame].squeeze().numpy()
        line.set_data(t, y)
        ax.set_title(f"{title} - 轨迹 {frame+1}/{trajectories.shape[0]}")
        return line,
    
    # 创建动画
    ani = FuncAnimation(fig, update, frames=trajectories.shape[0],
                        init_func=init, blit=True, interval=200)
    
    plt.tight_layout()
    plt.show()
    
    return ani

if __name__ == "__main__":
    # 生成数据
    trajectories, time, sample_rate, features = sine(
        double=False, 
        trajectories_to_sample=20,  # 减少轨迹数量以便于可视化
        t_nsamples=200, 
        num_pi=4
    )
    
    # 绘制5个样本轨迹
    plot_sine_waves(trajectories, time, num_plots=5, title="多个正弦波轨迹示例")
    
    # 绘制所有轨迹的分布
    plot_waveform_distribution(trajectories, time, title="所有正弦波轨迹分布")
    
    # 创建动画（需要交互环境才能看到动画效果）
    # 注意：在某些环境中可能无法显示动画，可以保存为文件后查看
    try:
        create_animation(trajectories, time, title="正弦波轨迹动画")
    except Exception as e:
        print(f"创建动画时出错: {e}")
        print("动画功能可能在当前环境中不可用，你可以尝试在支持交互的环境中运行。")    