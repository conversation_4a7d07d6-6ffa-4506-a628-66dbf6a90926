/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2025-07-03 18:38:42
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-07-03 18:38:53
 * @FilePath: \Multi-resolution-Energy-Forecasting-master\experiments\hello.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include<math.h>
#include<stdio.h>
void main() {
    float x, y, z;
    scanf("%f,%f", &x, &y);
    z = x / y;
    while(x > y) {
        if(fabs(z) > 1.0) {
            x = y;
            y = z;  // 假设原代码"y-汉;"为"y=z"的笔误
            z = x / y;
        } else {
            break;
        }
    }
    printf("%.1f", y);
}